/* You can add global styles to this file, and also import other style files */
@import "ngx-toastr/toastr";

.toast-container {
  position: fixed !important;
  z-index: 9999 !important;

  .ngx-toastr {
    margin-top: 6px;
  }
}

// overrides
@import "./scss/custom";
@import "./theme/colors";

@import "../node_modules/bootstrap/scss/bootstrap.scss";
@import "~bootstrap-icons/font/bootstrap-icons.css";

html,
body {
  width: 100%;
  font-family: "Poppins", serif;
  min-height: 100%;
  overflow-x: hidden;
}

$font-sizes: (
  "fs-48": 3rem,
  "fs-8": 0.5rem,
  "fs-10": 0.625rem,
  "fs-12": 0.75rem,
  "fs-14": 0.875rem,
  "fs-18": 1.125rem,
  "fs-24": 1.5rem,
);

// Loop through the font sizes map and generate the classes
@each $class, $size in $font-sizes {
  .#{$class} {
    font-size: $size;
  }
  @include responsive-font-sizes($class, if($class == "fs-48", 2rem, $size));
}

.required {
  color: var(--bs-danger);
}

.page-content {
  margin-top: calc(91px + 55px);
  padding-top: 1.875rem;
}

.solutions-container {
  .carousel-inner {
    padding-left: 80px !important;
    padding-right: 80px !important;
  }
  .carousel-control-prev,
  .carousel-control-next {
    width: 10% !important;
  }
}

.focile-avatar {
  height: 2.1875rem;
  width: 2.1875rem;
  border-radius: 50%;
}

.chip {
  border-radius: 16px;
  -webkit-font-smoothing: antialiased;
  margin-inline: 4px;
  margin-top: 4px;
  margin-bottom: 4px;
  padding-inline: 12px;
  padding-top: 7px;
  padding-bottom: 7px;
  display: inline-flex;
  position: relative;
  align-items: center;
  height: 24px;
  font-size: 12px;
  cursor: pointer;
  overflow: hidden;
  vertical-align: middle;
  box-sizing: border-box;
}

.about-container {
  max-height: 375px;
  overflow-y: auto;
}

.about-container ::-webkit-scrollbar {
  width: 0;
  height: 0;
}

th {
  background-color: #f2f4f6 !important;
}

.leaflet-bottom {
  display: none !important;
}

// .landing-page {
//   margin-top: calc(91px + 55px);
// }

.about-value-container {
  background-color: #f0f0f0;
  min-height: 5.675rem;
  padding: 8px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  max-height: 5.675rem;
  background-color: #f0f0f0;
}

.empty-state-content {
  text-align: center;
  color: #888; /* Gray text */
}

.empty-state-content h2 {
  margin: 0;
  font-size: 18px;
}

.empty-state-content p {
  margin: 10px 0 0;
  font-size: 16px;
}

.carousel-control-prev,
.carousel-control-next {
  height: 100px !important;
  top: 0% !important;
}

.tab-container {
  .nav-pills {
    justify-content: center !important;
  }
}

.cursor-pointer {
  cursor: pointer;
}

#mainNavBar {
  .popover-content {
    padding: 0 !important;
  }
}

.fc-container {
  max-width: 1170px;
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-right: auto;
  margin-left: auto;
}

.breadcumb {
  z-index: 11;
  position: relative;
  li {
    a {
      text-decoration: none;
    }

    &.breadcrumb-item + .breadcrumb-item::before {
      content: "-";
    }
  }
}

ng-select div:has(span.ng-tag-label) {
  background: var(--bs-primary) !important;
  color: white !important;
}
.fc-content-box {
  h5 {
    line-height: 1.5;
  }
}

// TinyMCE z-index fixes for modals
.tox-tinymce-aux {
  z-index: 10000 !important;
}

.tox-menu {
  z-index: 10001 !important;
}

.tox-dialog-wrap {
  z-index: 10002 !important;
}

.tox-dialog {
  z-index: 10003 !important;
}

.tox-collection {
  z-index: 10004 !important;
}

.tox-pop {
  z-index: 10005 !important;
}

.tox-floatpanel {
  z-index: 10006 !important;
}

.tox-silver-sink {
  z-index: 10007 !important;
}

// Ensure TinyMCE dropdowns appear above modal backdrops
.modal .tox-tinymce {
  position: relative;
  z-index: 1;
}

@media (max-width: 768px) {
  .empty-state-content {
    p {
      font-size: 14px;
    }
    h2 {
      font-size: 1rem;
    }
  }
  body {
    font-size: 14px;
  }
}
